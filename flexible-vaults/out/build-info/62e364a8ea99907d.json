{"id": "62e364a8ea99907d", "source_id_to_path": {"0": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Base.sol", "1": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdAssertions.sol", "2": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdChains.sol", "3": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdCheats.sol", "4": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdError.sol", "5": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdInvariant.sol", "6": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdJson.sol", "7": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdMath.sol", "8": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdStorage.sol", "9": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdStyle.sol", "10": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdToml.sol", "11": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdUtils.sol", "12": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Test.sol", "13": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Vm.sol", "14": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/console.sol", "15": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/console2.sol", "16": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC165.sol", "17": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC20.sol", "18": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC721.sol", "19": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IMulticall3.sol", "20": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC20.sol", "21": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC721.sol", "22": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/safeconsole.sol", "23": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol", "24": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "25": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "26": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "27": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "28": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol", "29": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol", "30": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "31": "src/hooks/LidoDepositHook.sol", "32": "src/interfaces/external/tokens/IWETH.sol", "33": "src/interfaces/external/tokens/IWSTETH.sol", "34": "src/interfaces/hooks/IHook.sol", "35": "src/libraries/TransferLibrary.sol", "36": "test/poc/LidoDepositHookVulnerabilityPOC.t.sol"}, "language": "Solidity"}